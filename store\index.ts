import { configureStore } from "@reduxjs/toolkit"
import uiReducer from "./uiSlice"
import sourcesReducer from "./sourcesSlice"
import chatReducer from "./chatSlice"
import queriesReducer from "./queriesSlice"

export const store = configureStore({
  reducer: {
    ui: uiReducer,
    sources: sourcesReducer,
    chat: chatReducer,
    queries: queriesReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["chat/addMessage", "queries/addQuery"],
        ignoredActionPaths: ["payload.timestamp", "payload.createdAt", "payload.messages"],
        ignoredPaths: ["chat.currentMessages", "queries.queries"],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
