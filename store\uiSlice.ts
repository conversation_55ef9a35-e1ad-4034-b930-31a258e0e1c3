import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

export type ActiveTab = "chat" | "queries"
export type ChatView = "chat" | "thinking" | "response"

interface UiState {
  activeTab: ActiveTab
  chatView: ChatView
}

const initialState: UiState = {
  activeTab: "chat",
  chatView: "chat",
}

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    setActiveTab(state, action: PayloadAction<ActiveTab>) {
      state.activeTab = action.payload
      // When switching to chat tab, determine if it's a new chat or a loaded one.
      // This logic is getting complex, a listener middleware would be better,
      // but for now, we can reset. The loading logic will set it to 'response'.
      if (action.payload === "chat") {
        // We'll let the query loading logic decide the view.
        // If no query is active, it should be 'chat'.
      }
    },
    setChatView(state, action: PayloadAction<ChatView>) {
      state.chatView = action.payload
    },
  },
})

export const { setActiveTab, setChatView } = uiSlice.actions
export default uiSlice.reducer
